// Hauling QR Trip Management System - Service Worker
// PWA Service Worker with Offline Support and Background Sync

const CACHE_NAME = 'hauling-qr-v1.9.0';
const OFFLINE_CACHE = 'hauling-qr-offline-v1.9.0';
const CHUNK_CACHE = 'hauling-qr-chunks-v1.9.0';

// Global variable to track PWA mode status from clients
let clientPWAMode = false;
let pwaStatusLastUpdated = null;

// Core app files to cache for offline functionality
const CORE_CACHE_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/offline-fallback.html',
  '/debug-pwa.html',
  '/driver-connect',
  '/trip-scanner'
];

// Dynamic chunk caching strategy
let DYNAMIC_CHUNKS = [];

// Simplified PWA mode detection with debouncing to prevent infinite refresh loops
let pwaDetectionInProgress = false;
let pwaDetectionDebounceTimer = null;

async function detectPWAMode() {
  try {
    // Use cached status if available and recent (within last 5 minutes to reduce detection frequency)
    const now = Date.now();
    if (pwaStatusLastUpdated && (now - pwaStatusLastUpdated) < 300000) {
      return clientPWAMode;
    }

    // Prevent concurrent detection attempts that could cause loops
    if (pwaDetectionInProgress) {
      return clientPWAMode || false;
    }

    // Debounce detection requests to prevent rapid successive calls
    if (pwaDetectionDebounceTimer) {
      clearTimeout(pwaDetectionDebounceTimer);
    }

    return new Promise((resolve) => {
      pwaDetectionDebounceTimer = setTimeout(async () => {
        pwaDetectionInProgress = true;

        try {
          const clients = await self.clients.matchAll();

          if (clients.length > 0) {
            // Simple one-time request without polling intervals
            const requestMessage = {
              type: 'REQUEST_PWA_MODE',
              timestamp: new Date().toISOString()
            };

            clients[0].postMessage(requestMessage);

            // Single timeout without polling intervals to prevent loops
            const timeout = setTimeout(() => {
              pwaDetectionInProgress = false;
              resolve(clientPWAMode || false);
            }, 2000);

            // Set up one-time response handler to avoid memory leaks
            const messageHandler = (event) => {
              if (event.data?.type === 'PWA_MODE_RESPONSE') {
                clearTimeout(timeout);
                self.removeEventListener('message', messageHandler);
                pwaDetectionInProgress = false;
                resolve(event.data.isPWA || false);
              }
            };

            self.addEventListener('message', messageHandler);
          } else {
            // No clients available, default to false
            pwaDetectionInProgress = false;
            resolve(false);
          }
        } catch (error) {
          console.error('[SW] Error in PWA mode detection:', error);
          pwaDetectionInProgress = false;
          resolve(false);
        }
      }, 500); // 500ms debounce delay to prevent rapid calls
    });

  } catch (error) {
    console.error('[SW] Critical error in detectPWAMode:', error);
    pwaDetectionInProgress = false;
    return false;
  }
}

// Fetch and cache all JavaScript chunks from asset-manifest.json
async function loadAssetManifest() {
  try {
    const response = await fetch('/asset-manifest.json');
    const manifest = await response.json();

    // Extract all JavaScript, CSS, and WASM files
    const assets = [];
    for (const [key, path] of Object.entries(manifest.files)) {
      if (path.endsWith('.js') || path.endsWith('.css') || path.endsWith('.wasm')) {
        assets.push(path);
      }
    }

    DYNAMIC_CHUNKS = assets;
    console.log('[SW] Loaded asset manifest:', assets.length, 'assets found');
    return assets;
  } catch (error) {
    console.error('[SW] Failed to load asset manifest:', error);
    // Fallback to common chunk patterns including WASM
    return [
      '/static/css/main.css',
      '/static/js/main.js',
      '/static/js/vendors-node_modules_yudiel_react-qr-scanner_dist_index_esm_mjs.chunk.js'
    ];
  }
}

// API endpoints that can work offline (removed unused variable)

// Install event - cache core files and all JavaScript chunks
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');

  event.waitUntil(
    Promise.all([
      // Cache core files
      caches.open(CACHE_NAME).then((cache) => {
        console.log('[SW] Caching core files');
        return cache.addAll(CORE_CACHE_FILES);
      }),

      // Load and cache all JavaScript chunks
      loadAssetManifest().then((assets) => {
        return caches.open(CHUNK_CACHE).then((cache) => {
          console.log('[SW] Caching JavaScript chunks:', assets.length, 'files');
          // Cache chunks in batches to avoid overwhelming the browser
          return cacheInBatches(cache, assets, 5);
        });
      })
    ])
    .then(() => {
      console.log('[SW] All files cached successfully');
      return self.skipWaiting(); // Activate immediately
    })
    .catch((error) => {
      console.error('[SW] Failed to cache files:', error);
    })
  );
});

// Cache files in batches to prevent overwhelming the browser
async function cacheInBatches(cache, urls, batchSize = 5) {
  for (let i = 0; i < urls.length; i += batchSize) {
    const batch = urls.slice(i, i + batchSize);
    try {
      await Promise.all(
        batch.map(async (url) => {
          try {
            const response = await fetch(url);
            if (response.ok) {
              await cache.put(url, response);
              // Reduced logging - only log errors or important caches
            }
          } catch (error) {
            console.warn('[SW] Failed to cache:', url, error);
          }
        })
      );
    } catch (error) {
      console.warn('[SW] Batch caching error:', error);
    }
  }
}

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME &&
                cacheName !== OFFLINE_CACHE &&
                cacheName !== CHUNK_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
            return Promise.resolve(); // Return resolved promise for non-matching caches
          })
        );
      })
      .then(() => {
        console.log('[SW] Service worker activated');
        return self.clients.claim(); // Take control immediately
      })
  );
});

// Fetch event - handle network requests with offline fallback
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // CRITICAL: Skip ALL webpack hot-update files and development resources
  // This prevents the service worker from intercepting these requests entirely
  if (url.pathname.includes('.hot-update.') || 
      url.pathname.includes('webpack') ||
      url.pathname.includes('sockjs-node') ||
      url.pathname.includes('__webpack_dev_server__') ||
      url.search.includes('hot=true') ||
      url.search.includes('live-reload') ||
      url.pathname.includes('.chunk.js.map') ||
      url.pathname.includes('main.') ||
      url.pathname.match(/\w+\.\w+\.hot-update\.(js|json)$/) ||
      url.href.includes('hot-update')) {
    // COMPLETELY BYPASS service worker for these requests
    console.log('[SW] BYPASSING hot-update file:', url.pathname);
    return;
  }

  // Check if the request is coming from PWA pages (driver-connect or trip-scanner)
  const referrer = request.referrer ? new URL(request.referrer) : null;
  const isPWAPage = referrer && (
    referrer.pathname === '/driver-connect' || 
    referrer.pathname === '/trip-scanner'
  );

  // Handle API requests - ONLY intercept if from PWA pages AND offline
  if (url.pathname.startsWith('/api/')) {
    // Only intercept API requests from PWA pages when offline
    if (isPWAPage && !navigator.onLine) {
      event.respondWith(handleApiRequest(request));
    }
    // Otherwise, let all API requests pass through normally
    return;
  }

  // Handle app navigation
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigation(request));
    return;
  }

  // Handle static assets (including WASM files)
  event.respondWith(handleStaticAssets(request));
});

// Handle API requests with offline fallback (only called when offline from PWA pages)
async function handleApiRequest(request) {
  console.log('[SW] Handling offline API request from PWA page');
  
  // Try cache first for offline requests
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    console.log('[SW] Serving API response from cache for PWA page');
    return cachedResponse;
  }

  // Return offline indicator for scan endpoints
  if (request.url.includes('/api/scanner/')) {
    return new Response(
      JSON.stringify({
        success: false,
        offline: true,
        message: 'Offline mode - scan will be queued for sync'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // Return offline indicator for driver endpoints
  if (request.url.includes('/api/driver/')) {
    return new Response(
      JSON.stringify({
        success: false,
        offline: true,
        message: 'Offline mode - connection will be queued for sync'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // For other API endpoints from PWA pages, return a generic offline response
  return new Response(
    JSON.stringify({
      success: false,
      offline: true,
      message: 'Service unavailable offline'
    }),
    {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}

// Handle navigation requests with improved error handling to prevent infinite refresh loops
let navigationInProgress = new Set();

async function handleNavigation(request) {
  const url = new URL(request.url);
  const requestKey = `${request.method}:${url.pathname}`;

  // Prevent concurrent navigation requests for the same URL to avoid loops
  if (navigationInProgress.has(requestKey)) {
    console.log('[SW] Navigation already in progress for:', url.pathname);
    // Return a simple response to break potential loops
    return new Response('Navigation in progress', { status: 202 });
  }

  navigationInProgress.add(requestKey);

  try {
    // Try network first with timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    try {
      const response = await fetch(request, {
        signal: controller.signal,
        cache: 'no-cache' // Prevent caching issues that could cause loops
      });
      clearTimeout(timeoutId);

      if (response.ok) {
        return response;
      }
      throw new Error(`Navigation failed: ${response.status}`);
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }

  } catch (error) {
    console.log('[SW] Navigation failed for:', url.pathname, error.message);

    // Only attempt PWA mode detection for specific routes to prevent unnecessary loops
    const driverConnectRoutes = ['/driver-connect'];
    const isDriverConnectRoute = driverConnectRoutes.some(route => url.pathname.startsWith(route));

    if (isDriverConnectRoute) {
      // Simplified PWA mode check - avoid complex detection that could cause loops
      const isPWAMode = clientPWAMode || false; // Use cached value only

      if (isPWAMode) {
        console.log('[SW] Serving cached content for driver-connect route in PWA mode');

        // Try to serve cached main app
        const cachedResponse = await caches.match('/') || await caches.match('/index.html');
        if (cachedResponse) {
          return cachedResponse;
        }

        // Fallback to offline page if available
        const offlineFallback = await caches.match('/offline-fallback.html');
        if (offlineFallback) {
          return offlineFallback;
        }
      } else {
        // Browser mode - let it fail normally to show "site can't be reached"
        console.log('[SW] Browser mode - allowing normal navigation failure');
        throw error;
      }
    }

    // For other routes (like trip-scanner), serve cached content regardless of PWA mode
    const otherPWARoutes = ['/trip-scanner'];
    const isOtherPWARoute = otherPWARoutes.some(route => url.pathname.startsWith(route));

    if (isOtherPWARoute) {
      console.log('[SW] Serving cached content for other PWA route:', url.pathname);

      const cachedResponse = await caches.match('/') || await caches.match('/index.html');
      if (cachedResponse) {
        return cachedResponse;
      }

      const offlineFallback = await caches.match('/offline-fallback.html');
      if (offlineFallback) {
        return offlineFallback;
      }
    }

    // For all other routes, let them fail normally
    console.log('[SW] No cached content to serve - allowing normal navigation failure');
    throw error;

  } finally {
    // Always clean up navigation tracking to prevent memory leaks
    navigationInProgress.delete(requestKey);
  }
}

// Handle static assets with enhanced chunk caching
async function handleStaticAssets(request) {
  try {
    const url = new URL(request.url);
    
    // NEVER cache development/hot-update files - AGGRESSIVE CHECK
    if (url.pathname.includes('.hot-update.') || 
        url.pathname.includes('webpack') ||
        url.pathname.includes('sockjs-node') ||
        url.pathname.includes('__webpack_dev_server__') ||
        url.pathname.includes('main.') ||
        url.pathname.match(/\w+\.\w+\.hot-update\.(js|json)$/) ||
        url.href.includes('hot-update')) {
      console.log('[SW] NEVER CACHING hot-update file:', url.pathname);
      return fetch(request); // Always fetch from network, never cache
    }

    // Try cache first - check all cache stores
    let cachedResponse = await caches.match(request);
    if (!cachedResponse) {
      // Check chunk cache specifically for JavaScript and WASM files
      if (request.url.includes('/static/js/') || 
          request.url.includes('.chunk.js') || 
          request.url.includes('.wasm')) {
        cachedResponse = await caches.match(request, { cacheName: CHUNK_CACHE });
      }
    }

    if (cachedResponse) {
      // Reduced logging - only log for non-hot-update files
      if (!request.url.includes('.hot-update.')) {
        console.log('[SW] Serving from cache:', request.url);
      }
      return cachedResponse;
    }

    // If online, fetch from network and cache (but not development files)
    if (navigator.onLine) {
      const response = await fetch(request);
      if (response.ok && !url.pathname.includes('.hot-update.')) {
        // Determine which cache to use
        const cache = (request.url.includes('/static/js/') || 
                      request.url.includes('.chunk.js') || 
                      request.url.includes('.wasm'))
          ? await caches.open(CHUNK_CACHE)
          : await caches.open(CACHE_NAME);
        cache.put(request, response.clone());
        // Reduced logging - only log important assets, not hot-updates
        if (!request.url.includes('.hot-update.')) {
          console.log('[SW] Cached new asset:', request.url);
        }
      }
      return response;
    } else {
      // Offline and no cache - provide fallback
      throw new Error('Asset not available offline');
    }
  } catch (error) {
    console.log('[SW] Static asset failed:', request.url, error.message);

    // For JavaScript chunks, provide a fallback that prevents app crash
    if (request.url.includes('.chunk.js') || request.url.includes('/static/js/')) {
      return new Response(
        `console.warn('Chunk ${request.url} not available offline - providing fallback');`,
        {
          status: 200,
          headers: { 'Content-Type': 'application/javascript' }
        }
      );
    }
    
    // For WASM files, provide a specific error that the QR scanner can handle
    if (request.url.includes('.wasm')) {
      console.warn('[SW] WASM file not available offline:', request.url);
      // Return a 404 so the QR scanner can fall back to alternative detection methods
      return new Response('WASM file not available offline', {
        status: 404,
        statusText: 'Not Found'
      });
    }

    throw error;
  }
}

// Determine if API response should be cached
function shouldCacheApiResponse(url) {
  // Cache location and truck data for offline access
  return url.includes('/api/locations') || 
         url.includes('/api/trucks') ||
         url.includes('/api/assignments');
}

// Background Sync for offline data
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);

  // Check if this is Driver Connect PWA - skip background sync to avoid conflicts with manual sync
  const isDriverConnectPWA = clientPWAMode === true &&
                              (self.registration.scope.includes('/driver-connect') ||
                               event.tag === 'driver-connect-sync');

  if (isDriverConnectPWA && event.tag === 'driver-connect-sync') {
    console.log('[SW] Skipping driver-connect-sync for PWA to avoid manual sync conflicts');
    return;
  }

  if (event.tag === 'trip-scan-sync') {
    event.waitUntil(syncOfflineData('trip-scans'));
  } else if (event.tag === 'driver-connect-sync') {
    event.waitUntil(syncOfflineData('driver-connections'));
  } else if (event.tag === 'comprehensive-sync') {
    event.waitUntil(syncOfflineData('all'));
  }
});

// Sync offline data using centralized background sync service
async function syncOfflineData(syncType) {
  console.log(`[SW] Starting ${syncType} sync...`);

  try {
    // Send message to main thread to trigger sync
    const clients = await self.clients.matchAll();

    for (const client of clients) {
      client.postMessage({
        type: 'TRIGGER_SYNC',
        syncType: syncType,
        source: 'service-worker'
      });
    }

    console.log(`[SW] ${syncType} sync message sent to clients`);
  } catch (error) {
    console.error(`[SW] Failed to trigger ${syncType} sync:`, error);
  }
}

// Removed unused syncOfflineConnections function

// Enhanced message handling for communication with main app with comprehensive error handling
self.addEventListener('message', (event) => {
  try {
    console.log('[SW] Received message:', event.data);

    // Validate message structure
    if (!event.data || typeof event.data !== 'object') {
      console.warn('[SW] Invalid message format received:', event.data);
      return;
    }

    const messageType = event.data.type;
    const messageSource = event.data.source || 'unknown';
    const messageTimestamp = event.data.timestamp || new Date().toISOString();

    console.log('[SW] Processing message type:', messageType, 'from:', messageSource, 'at:', messageTimestamp);

    // Handle skip waiting requests
    if (messageType === 'SKIP_WAITING') {
      try {
        console.log('[SW] Processing skip waiting request');
        self.skipWaiting();
      } catch (skipError) {
        console.error('[SW] Error during skip waiting:', skipError);
      }
    }

    // Handle cache status requests
    if (messageType === 'GET_CACHE_STATUS') {
      try {
        console.log('[SW] Processing cache status request');
        if (event.ports && event.ports[0]) {
          event.ports[0].postMessage({
            type: 'CACHE_STATUS',
            cached: true, // We'll implement proper cache checking later
            timestamp: new Date().toISOString(),
            source: 'service-worker'
          });
        } else {
          console.warn('[SW] No port available for cache status response');
        }
      } catch (cacheError) {
        console.error('[SW] Error handling cache status request:', cacheError);
      }
    }

    // Handle PWA debug test messages
    if (messageType === 'PWA_DEBUG_TEST') {
      try {
        console.log('[SW] PWA debug test received:', event.data);

        // Send debug response with service worker status
        if (event.source) {
          event.source.postMessage({
            type: 'PWA_DEBUG_RESPONSE',
            timestamp: new Date().toISOString(),
            source: 'service-worker',
            serviceWorkerInfo: {
              scope: self.registration?.scope,
              state: self.registration?.active?.state,
              clientPWAMode: clientPWAMode,
              pwaStatusLastUpdated: pwaStatusLastUpdated,
              cacheNames: [CACHE_NAME, OFFLINE_CACHE, CHUNK_CACHE]
            },
            requestId: event.data.requestId || null
          });
          console.log('[SW] PWA debug response sent');
        }
      } catch (debugError) {
        console.error('[SW] Error handling PWA debug test:', debugError);
      }
    }

    // Enhanced PWA mode status handling from clients
    if (messageType === 'PWA_MODE_STATUS') {
      try {
        const previousMode = clientPWAMode;
        const newMode = event.data.isPWA;

        // Validate PWA mode value
        if (typeof newMode !== 'boolean') {
          console.warn('[SW] Invalid PWA mode value received:', newMode, 'expected boolean');
          return;
        }

        clientPWAMode = newMode;
        pwaStatusLastUpdated = Date.now();

        console.log('[SW] PWA mode status updated:', clientPWAMode, 'at', new Date().toISOString());

        // Log mode changes with enhanced context
        if (previousMode !== clientPWAMode) {
          console.log('[SW] PWA mode changed from', previousMode, 'to', clientPWAMode);

          // Log additional context for mode changes
          console.log('[SW] PWA mode change context:', {
            previousMode,
            newMode: clientPWAMode,
            source: messageSource,
            timestamp: messageTimestamp,
            currentPath: event.data.currentPath,
            userAgent: event.data.userAgent?.substring(0, 100) + '...' || 'unknown'
          });
        }

        // Log additional details if provided
        if (event.data.currentPath) {
          console.log('[SW] PWA mode status for path:', event.data.currentPath);
        }
        if (event.data.displayMode) {
          console.log('[SW] Display mode:', event.data.displayMode);
        }
        if (event.data.detectionMethods) {
          console.log('[SW] PWA detection methods used:', event.data.detectionMethods);
        }

        // Send confirmation back to client with error handling
        if (event.source) {
          try {
            event.source.postMessage({
              type: 'PWA_MODE_CONFIRMATION',
              confirmedMode: clientPWAMode,
              previousMode: previousMode,
              timestamp: new Date().toISOString(),
              source: 'service-worker',
              requestId: event.data.requestId || null
            });
            console.log('[SW] PWA mode confirmation sent to client');
          } catch (confirmError) {
            console.error('[SW] Error sending PWA mode confirmation:', confirmError);
          }
        } else {
          console.warn('[SW] No event source available for PWA mode confirmation');
        }
        
      } catch (pwaError) {
        console.error('[SW] Error processing PWA mode status:', pwaError);
      }
    }

    // Handle location change notifications with enhanced logging
    if (messageType === 'LOCATION_CHANGE') {
      try {
        console.log('[SW] Location change notification:', event.data.currentPath, 'PWA:', event.data.isPWA);
        
        // Update PWA mode if provided and valid
        if (typeof event.data.isPWA === 'boolean') {
          const previousMode = clientPWAMode;
          clientPWAMode = event.data.isPWA;
          pwaStatusLastUpdated = Date.now();
          
          if (previousMode !== clientPWAMode) {
            console.log('[SW] PWA mode updated via location change from', previousMode, 'to', clientPWAMode);
          }
        }
        
        // Log navigation context
        console.log('[SW] Navigation context:', {
          path: event.data.currentPath,
          isPWA: event.data.isPWA,
          source: messageSource,
          timestamp: messageTimestamp
        });
        
      } catch (locationError) {
        console.error('[SW] Error processing location change:', locationError);
      }
    }

    // Handle client ready notifications with enhanced response
    if (messageType === 'CLIENT_READY') {
      try {
        console.log('[SW] Client ready notification received from:', messageSource);
        
        // Log client capabilities if provided
        if (event.data.capabilities) {
          console.log('[SW] Client capabilities:', event.data.capabilities);
        }
        
        // Send service worker ready confirmation with enhanced info
        if (event.source) {
          try {
            event.source.postMessage({
              type: 'SERVICE_WORKER_READY',
              timestamp: new Date().toISOString(),
              currentPWAMode: clientPWAMode,
              pwaStatusAge: pwaStatusLastUpdated ? Date.now() - pwaStatusLastUpdated : null,
              source: 'service-worker',
              swVersion: CACHE_NAME,
              capabilities: {
                backgroundSync: 'sync' in self.registration,
                pushNotifications: 'showNotification' in self.registration,
                cacheAPI: 'caches' in self
              }
            });
            console.log('[SW] Service worker ready confirmation sent');
          } catch (readyError) {
            console.error('[SW] Error sending ready confirmation:', readyError);
          }
        } else {
          console.warn('[SW] No event source available for ready confirmation');
        }
        
      } catch (clientReadyError) {
        console.error('[SW] Error processing client ready notification:', clientReadyError);
      }
    }

    // Handle legacy PWA mode information (for backward compatibility)
    if (messageType === 'PWA_MODE_INFO') {
      try {
        console.log('[SW] Processing legacy PWA mode info');
        const previousMode = clientPWAMode;
        clientPWAMode = event.data.isPWAMode;
        pwaStatusLastUpdated = Date.now();
        
        console.log('[SW] PWA mode updated (legacy):', clientPWAMode, 'for path:', event.data.currentPath);
        
        if (previousMode !== clientPWAMode) {
          console.log('[SW] Legacy PWA mode change from', previousMode, 'to', clientPWAMode);
        }
        
      } catch (legacyError) {
        console.error('[SW] Error processing legacy PWA mode info:', legacyError);
      }
    }

    // Handle sync trigger messages from main thread
    if (messageType === 'TRIGGER_SYNC') {
      try {
        const { syncType } = event.data;
        console.log(`[SW] Triggering ${syncType} sync from main thread`);

        // Validate sync type
        if (!syncType || typeof syncType !== 'string') {
          console.warn('[SW] Invalid sync type received:', syncType);
          return;
        }

        // Delegate sync back to main thread via message
        if (event.source) {
          try {
            event.source.postMessage({
              type: 'SYNC_DELEGATED',
              syncType: syncType,
              message: `${syncType} sync delegated to main thread`,
              timestamp: new Date().toISOString(),
              source: 'service-worker'
            });
            console.log(`[SW] ${syncType} sync delegation message sent`);
          } catch (syncError) {
            console.error('[SW] Error sending sync delegation message:', syncError);
          }
        } else {
          console.warn('[SW] No event source available for sync delegation');
        }
        
      } catch (triggerError) {
        console.error('[SW] Error processing sync trigger:', triggerError);
      }
    }

    // Enhanced requests for PWA mode status from service worker
    if (messageType === 'REQUEST_PWA_MODE') {
      try {
        console.log('[SW] Processing PWA mode request from client');
        console.log('[SW] Current PWA mode status:', {
          mode: clientPWAMode,
          lastUpdated: pwaStatusLastUpdated,
          age: pwaStatusLastUpdated ? Date.now() - pwaStatusLastUpdated : null
        });
        
        // This message will be handled by the PWA status hook
        // The hook will respond with enhanced PWA mode detection
        // No direct response needed here as the client will send PWA_MODE_STATUS
        
      } catch (requestError) {
        console.error('[SW] Error processing PWA mode request:', requestError);
      }
    }

    // Handle unknown message types
    if (!['SKIP_WAITING', 'GET_CACHE_STATUS', 'PWA_MODE_STATUS', 'LOCATION_CHANGE', 
          'CLIENT_READY', 'PWA_MODE_INFO', 'TRIGGER_SYNC', 'REQUEST_PWA_MODE'].includes(messageType)) {
      console.warn('[SW] Unknown message type received:', messageType, 'from:', messageSource);
    }

  } catch (error) {
    console.error('[SW] Critical error in message handler:', error);
    console.error('[SW] Message that caused error:', event.data);
    console.error('[SW] Error context:', {
      timestamp: new Date().toISOString(),
      eventSource: !!event.source,
      eventPorts: event.ports?.length || 0
    });
  }
});



console.log('[SW] Service worker script loaded');
