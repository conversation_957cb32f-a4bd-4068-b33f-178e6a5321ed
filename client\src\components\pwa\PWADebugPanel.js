import React, { useState, useEffect, useCallback } from 'react';
import { driverConnectOffline } from '../../services/driverConnectOffline';
import { backgroundSync } from '../../services/backgroundSync';
import {
  detectPWAEnvironment,
  testServiceWorkerCommunication,
  compareSyncBehavior,
  generatePWAInvestigationReport
} from '../../utils/pwaInvestigation';

/**
 * PWA-Specific Debug Panel
 * Provides in-PWA debugging capabilities without requiring browser dev tools
 * Focuses on service worker interactions and sync status investigation
 */
const PWADebugPanel = ({ isVisible, onClose }) => {
  const [debugLogs, setDebugLogs] = useState([]);
  const [pwaStatus, setPWAStatus] = useState({});
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState({});
  const [syncDetails, setSyncDetails] = useState({});
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Add debug log entry with timestamp
  const addDebugLog = useCallback((message, type = 'info', data = null) => {
    const logEntry = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      message,
      type, // 'info', 'warning', 'error', 'success'
      data: data ? JSON.stringify(data, null, 2) : null
    };
    
    setDebugLogs(prev => [logEntry, ...prev.slice(0, 49)]); // Keep last 50 logs
  }, []);

  // Detect PWA mode and environment using investigation utilities
  const detectPWAEnvironmentEnhanced = useCallback(() => {
    const pwaInfo = detectPWAEnvironment();
    setPWAStatus(pwaInfo);
    addDebugLog('PWA Environment Detected', 'info', pwaInfo);
    return pwaInfo;
  }, [addDebugLog]);

  // Check service worker status and registration
  const checkServiceWorkerStatus = useCallback(async () => {
    if (!('serviceWorker' in navigator)) {
      const status = { supported: false, error: 'Service Worker not supported' };
      setServiceWorkerStatus(status);
      addDebugLog('Service Worker Not Supported', 'error', status);
      return status;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      const controller = navigator.serviceWorker.controller;
      
      const status = {
        supported: true,
        hasRegistration: !!registration,
        hasController: !!controller,
        scope: registration?.scope,
        state: registration?.active?.state,
        scriptURL: registration?.active?.scriptURL,
        updateViaCache: registration?.updateViaCache
      };
      
      setServiceWorkerStatus(status);
      addDebugLog('Service Worker Status', 'info', status);
      return status;
    } catch (error) {
      const status = { supported: true, error: error.message };
      setServiceWorkerStatus(status);
      addDebugLog('Service Worker Error', 'error', status);
      return status;
    }
  }, [addDebugLog]);

  // Test service worker message communication using investigation utilities
  const testServiceWorkerCommunicationEnhanced = useCallback(async () => {
    addDebugLog('Testing Service Worker Communication', 'info');

    try {
      const result = await testServiceWorkerCommunication();

      if (result.error) {
        addDebugLog('Service Worker Communication Failed', 'error', result);
        return false;
      } else {
        addDebugLog('Service Worker Communication Successful', 'success', result);
        return true;
      }
    } catch (error) {
      addDebugLog('Service Worker Communication Error', 'error', { error: error.message });
      return false;
    }
  }, [addDebugLog]);

  // Get detailed sync status and queue information
  const getSyncDetails = useCallback(async () => {
    try {
      const connectionCount = await driverConnectOffline.getPendingCount();
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      
      const details = {
        queuedConnections: connectionCount,
        pendingConnections: pendingConnections.map(conn => ({
          id: conn.id,
          action: conn.apiPayload?.action,
          timestamp: conn.timestamp,
          status: conn.status,
          driverId: conn.apiPayload?.driver_qr_data?.employee_id,
          truckId: conn.apiPayload?.truck_qr_data?.id
        })),
        networkStatus: navigator.onLine ? 'online' : 'offline',
        lastSyncAttempt: localStorage.getItem('lastSyncAttempt'),
        syncErrors: localStorage.getItem('syncErrors')
      };
      
      setSyncDetails(details);
      addDebugLog('Sync Details Retrieved', 'info', details);
      return details;
    } catch (error) {
      addDebugLog('Failed to Get Sync Details', 'error', { error: error.message });
      return null;
    }
  }, [addDebugLog]);

  // Test manual sync operation
  const testManualSync = useCallback(async () => {
    addDebugLog('Starting Manual Sync Test', 'info');

    try {
      const results = await backgroundSync.startSync();
      addDebugLog('Manual Sync Completed', 'success', results);

      // Refresh sync details after sync
      await getSyncDetails();

      return results;
    } catch (error) {
      addDebugLog('Manual Sync Failed', 'error', { error: error.message });
      return null;
    }
  }, [addDebugLog, getSyncDetails]);

  // Run comprehensive PWA investigation
  const runComprehensiveInvestigation = useCallback(async () => {
    addDebugLog('Starting Comprehensive PWA Investigation', 'info');
    setIsRefreshing(true);

    try {
      const report = await generatePWAInvestigationReport();
      addDebugLog('PWA Investigation Complete', 'success', report);

      // Log specific findings
      if (report.recommendations.length > 0) {
        report.recommendations.forEach(rec => {
          addDebugLog(`Recommendation (${rec.type})`, rec.type === 'error' ? 'error' : 'warning', { message: rec.message });
        });
      }

      // Update states with investigation results
      setPWAStatus(report.environment);
      setServiceWorkerStatus({
        supported: !report.serviceWorkerTest.error,
        hasController: report.serviceWorkerTest.success,
        communicationTest: report.serviceWorkerTest
      });
      setSyncDetails(report.syncBehaviorTest);

      return report;
    } catch (error) {
      addDebugLog('PWA Investigation Failed', 'error', { error: error.message });
      return null;
    } finally {
      setIsRefreshing(false);
    }
  }, [addDebugLog]);

  // Refresh all debug information
  const refreshDebugInfo = useCallback(async () => {
    setIsRefreshing(true);
    addDebugLog('Refreshing Debug Information', 'info');

    try {
      await Promise.all([
        detectPWAEnvironmentEnhanced(),
        checkServiceWorkerStatus(),
        getSyncDetails(),
        testServiceWorkerCommunicationEnhanced()
      ]);

      addDebugLog('Debug Information Refreshed', 'success');
    } catch (error) {
      addDebugLog('Failed to Refresh Debug Info', 'error', { error: error.message });
    } finally {
      setIsRefreshing(false);
    }
  }, [detectPWAEnvironmentEnhanced, checkServiceWorkerStatus, getSyncDetails, testServiceWorkerCommunicationEnhanced, addDebugLog]);

  // Initialize debug panel
  useEffect(() => {
    if (isVisible) {
      refreshDebugInfo();
    }
  }, [isVisible, refreshDebugInfo]);

  // Clear debug logs
  const clearLogs = useCallback(() => {
    setDebugLogs([]);
    addDebugLog('Debug Logs Cleared', 'info');
  }, [addDebugLog]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 flex justify-between items-center">
          <h2 className="text-xl font-bold">PWA Debug Panel</h2>
          <div className="flex gap-2">
            <button
              onClick={refreshDebugInfo}
              disabled={isRefreshing}
              className="bg-blue-500 hover:bg-blue-400 px-3 py-1 rounded text-sm disabled:opacity-50"
            >
              {isRefreshing ? '🔄' : '🔄'} Refresh
            </button>
            <button
              onClick={onClose}
              className="bg-red-500 hover:bg-red-400 px-3 py-1 rounded text-sm"
            >
              ✕ Close
            </button>
          </div>
        </div>

        <div className="p-4 overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* PWA Status */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">PWA Environment</h3>
            <div className="bg-gray-100 p-3 rounded text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div><strong>PWA Mode:</strong> {pwaStatus.isPWAMode ? '✅ Yes' : '❌ No'}</div>
                <div><strong>Display Mode:</strong> {pwaStatus.displayMode}</div>
                <div><strong>Network:</strong> {pwaStatus.networkStatus}</div>
                <div><strong>Path:</strong> {pwaStatus.currentPath}</div>
              </div>
            </div>
          </div>

          {/* Service Worker Status */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Service Worker Status</h3>
            <div className="bg-gray-100 p-3 rounded text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div><strong>Supported:</strong> {serviceWorkerStatus.supported ? '✅ Yes' : '❌ No'}</div>
                <div><strong>Controller:</strong> {serviceWorkerStatus.hasController ? '✅ Yes' : '❌ No'}</div>
                <div><strong>State:</strong> {serviceWorkerStatus.state || 'Unknown'}</div>
                <div><strong>Scope:</strong> {serviceWorkerStatus.scope || 'Unknown'}</div>
              </div>
            </div>
          </div>

          {/* Sync Details */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Sync Status</h3>
            <div className="bg-gray-100 p-3 rounded text-sm">
              <div className="grid grid-cols-2 gap-2 mb-2">
                <div><strong>Queued Connections:</strong> {syncDetails.queuedConnections || 0}</div>
                <div><strong>Network:</strong> {syncDetails.networkStatus}</div>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={testManualSync}
                  className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm"
                >
                  🔄 Test Manual Sync
                </button>
                <button
                  onClick={runComprehensiveInvestigation}
                  disabled={isRefreshing}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm disabled:opacity-50"
                >
                  🔍 Full Investigation
                </button>
              </div>
            </div>
          </div>

          {/* Debug Logs */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-semibold">Debug Logs</h3>
              <button
                onClick={clearLogs}
                className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
              >
                🗑️ Clear Logs
              </button>
            </div>
            <div className="bg-black text-green-400 p-3 rounded text-xs font-mono max-h-64 overflow-y-auto">
              {debugLogs.length === 0 ? (
                <div className="text-gray-500">No debug logs yet...</div>
              ) : (
                debugLogs.map(log => (
                  <div key={log.id} className={`mb-2 ${
                    log.type === 'error' ? 'text-red-400' :
                    log.type === 'warning' ? 'text-yellow-400' :
                    log.type === 'success' ? 'text-green-400' :
                    'text-blue-400'
                  }`}>
                    <div>[{new Date(log.timestamp).toLocaleTimeString()}] {log.message}</div>
                    {log.data && (
                      <pre className="text-gray-300 ml-4 text-xs overflow-x-auto">
                        {log.data}
                      </pre>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PWADebugPanel;
